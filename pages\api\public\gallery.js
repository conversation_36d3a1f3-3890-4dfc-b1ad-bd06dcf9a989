/**
 * Public Gallery API
 * Provides gallery data for the frontend gallery page
 */

import { getAdminClient } from '@/lib/supabase';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      category,
      featured,
      limit = 50,
      offset = 0,
      include_categories = false
    } = req.query;

    const supabaseAdmin = getAdminClient();

    // Build query for gallery items
    let query = supabaseAdmin
      .from('gallery_items')
      .select(`
        id,
        title,
        description,
        category,
        main_image_url,
        gallery_images,
        featured,
        display_order,
        meta_title,
        meta_description,
        created_at
      `)
      .eq('status', 'active'); // Only return active items

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    if (featured !== undefined) {
      query = query.eq('featured', featured === 'true');
    }

    // Apply sorting (featured items first, then by display order, then by creation date)
    query = query.order('featured', { ascending: false })
                 .order('display_order', { ascending: true })
                 .order('created_at', { ascending: false });

    // Apply pagination
    query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    const { data: galleryItems, error } = await query;

    if (error) {
      console.error('Error fetching gallery items:', error);
      return res.status(500).json({ error: 'Failed to fetch gallery items' });
    }

    // Transform data to match the expected frontend format
    const transformedItems = (galleryItems || []).map(item => ({
      id: item.id,
      title: item.title || '',
      description: item.description || '',
      category: item.category || 'all',
      mainImage: item.main_image_url || '',
      images: item.gallery_images || [],
      featured: item.featured || false,
      displayOrder: item.display_order || 0,
      metaTitle: item.meta_title || item.title || '',
      metaDescription: item.meta_description || item.description || '',
      createdAt: item.created_at
    }));

    let responseData = {
      success: true,
      data: transformedItems,
      pagination: {
        total: transformedItems.length,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: transformedItems.length === parseInt(limit)
      }
    };

    // Optionally include categories
    if (include_categories === 'true') {
      const { data: categories, error: categoriesError } = await supabaseAdmin
        .from('gallery_categories')
        .select('*')
        .eq('status', 'active')
        .order('display_order', { ascending: true });

      if (!categoriesError) {
        responseData.categories = (categories || []).map(cat => ({
          id: cat.id,
          name: cat.name || '',
          slug: cat.slug || '',
          description: cat.description || '',
          color: cat.color || '#6a0dad',
          displayOrder: cat.display_order || 0
        }));
      }
    }

    // If no items found in database, return fallback data
    if (transformedItems.length === 0) {
      return res.status(200).json({
        success: true,
        data: getFallbackGalleryData(),
        fallback: true,
        message: 'Using fallback gallery data. Consider migrating to database.'
      });
    }

    return res.status(200).json(responseData);
  } catch (error) {
    console.error('Gallery API error:', error);
    
    // Return fallback data on error
    return res.status(200).json({
      success: true,
      data: getFallbackGalleryData(),
      fallback: true,
      error: 'Database error, using fallback data'
    });
  }
}

/**
 * Fallback gallery data (matches the original hardcoded data)
 */
function getFallbackGalleryData() {
  return [
    {
      id: 'face-art-1',
      title: 'Butterfly Face Art',
      description: 'Beautiful butterfly face art design',
      category: 'face',
      mainImage: '/images/gallery/fav/Butterfly.JPG',
      images: [{ src: '/images/gallery/fav/Butterfly.JPG', alt: 'Butterfly Face Art', caption: 'Butterfly Face Art' }],
      featured: true,
      displayOrder: 0
    },
    {
      id: 'face-art-2',
      title: 'Gold Leopard',
      description: 'Stunning gold leopard face paint',
      category: 'face',
      mainImage: '/images/gallery/fav/Gold Leopard.jpg',
      images: [{ src: '/images/gallery/fav/Gold Leopard.jpg', alt: 'Gold Leopard', caption: 'Gold Leopard' }],
      featured: true,
      displayOrder: 1
    },
    {
      id: 'face-art-3',
      title: 'Soft Face',
      description: 'Elegant soft face design',
      category: 'face',
      mainImage: '/images/gallery/fav/Soft Face.JPG',
      images: [{ src: '/images/gallery/fav/Soft Face.JPG', alt: 'Soft Face', caption: 'Soft Face' }],
      featured: true,
      displayOrder: 2
    },
    {
      id: 'face-art-4',
      title: 'Blue Sparkles',
      description: 'Sparkling blue face art',
      category: 'face',
      mainImage: '/images/gallery/fav/Blue Sparkles.JPG',
      images: [{ src: '/images/gallery/fav/Blue Sparkles.JPG', alt: 'Blue Sparkles', caption: 'Blue Sparkles' }],
      featured: true,
      displayOrder: 3
    },
    {
      id: 'face-art-5',
      title: 'Glitter Goddess',
      description: 'Glamorous glitter goddess design',
      category: 'face',
      mainImage: '/images/gallery/fav/Glitter Goddess.JPG',
      images: [{ src: '/images/gallery/fav/Glitter Goddess.JPG', alt: 'Glitter Goddess', caption: 'Glitter Goddess' }],
      featured: true,
      displayOrder: 4
    },
    {
      id: 'face-art-6',
      title: 'Glitter Goddess 2',
      description: 'Another stunning glitter goddess look',
      category: 'face',
      mainImage: '/images/gallery/fav/Glitter Goddess 2.JPG',
      images: [{ src: '/images/gallery/fav/Glitter Goddess 2.JPG', alt: 'Glitter Goddess 2', caption: 'Glitter Goddess 2' }],
      featured: true,
      displayOrder: 5
    },
    {
      id: 'body-art-1',
      title: 'Adult Body Suit',
      description: 'Professional body art suit design',
      category: 'body',
      mainImage: '/images/gallery/fav/Adult BodySuit.JPG',
      images: [{ src: '/images/gallery/fav/Adult BodySuit.JPG', alt: 'Adult Body Suit', caption: 'Adult Body Suit' }],
      featured: false,
      displayOrder: 6
    },
    {
      id: 'body-art-2',
      title: 'Camo Body Paint',
      description: 'Realistic camouflage body paint',
      category: 'body',
      mainImage: '/images/gallery/fav/Camo Bodypaint.JPG',
      images: [{ src: '/images/gallery/fav/Camo Bodypaint.JPG', alt: 'Camo Body Paint', caption: 'Camo Body Paint' }],
      featured: false,
      displayOrder: 7
    },
    {
      id: 'uv-art-1',
      title: 'UV Alex',
      description: 'Amazing UV reactive art',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Alex.JPG',
      images: [{ src: '/images/gallery/fav/UV Alex.JPG', alt: 'UV Alex', caption: 'UV Alex' }],
      featured: false,
      displayOrder: 8
    },
    {
      id: 'uv-art-2',
      title: 'UV Body Paint',
      description: 'Glowing UV body art',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Body Paint.JPG',
      images: [{ src: '/images/gallery/fav/UV Body Paint.JPG', alt: 'UV Body Paint', caption: 'UV Body Paint' }],
      featured: false,
      displayOrder: 9
    },
    {
      id: 'kids-1',
      title: 'Kids Butterfly',
      description: 'Fun butterfly design for kids',
      category: 'kids',
      mainImage: '/images/gallery/fav/Kids Butterfly.jpg',
      images: [{ src: '/images/gallery/fav/Kids Butterfly.jpg', alt: 'Kids Butterfly', caption: 'Kids Butterfly' }],
      featured: false,
      displayOrder: 10
    },
    {
      id: 'kids-2',
      title: 'Kids Tropical',
      description: 'Tropical themed kids face paint',
      category: 'kids',
      mainImage: '/images/gallery/fav/Kids Tropical.jpg',
      images: [{ src: '/images/gallery/fav/Kids Tropical.jpg', alt: 'Kids Tropical', caption: 'Kids Tropical' }],
      featured: false,
      displayOrder: 11
    },
    {
      id: 'events-1',
      title: 'OceanSoulVibes Light Box',
      description: 'Event photography with light box',
      category: 'events',
      mainImage: '/images/gallery/fav/OceanSoulVibes Light Box.JPG',
      images: [{ src: '/images/gallery/fav/OceanSoulVibes Light Box.JPG', alt: 'OceanSoulVibes Light Box', caption: 'OceanSoulVibes Light Box' }],
      featured: false,
      displayOrder: 12
    }
  ];
}
